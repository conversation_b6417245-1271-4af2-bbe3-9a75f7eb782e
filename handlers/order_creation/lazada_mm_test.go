package order_creation

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/golang/mock/gomock"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"

	"git.ninjavan.co/3pl/configs"
	"git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/order_creation/mocks"
	"git.ninjavan.co/3pl/handlers/order_creation/order_creation_interface"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	repoMocks "git.ninjavan.co/3pl/repositories/mocks"
	"git.ninjavan.co/3pl/utils"
)

type testSetup struct {
	ctrl                   *gomock.Controller
	mockOrderCreator       *mocks.MockOrderCreator
	mockParcelRepo         *repoMocks.MockParcelRepository
	mockOrderRequestRepo   *repoMocks.MockOrderRequestRepository
	mockShipmentParcelRepo *repoMocks.MockShipmentParcelRepository
	mockParcelItemRepo     *repoMocks.MockParcelItemRepository
	db                     *sql.DB
	mock                   sqlmock.Sqlmock
	creator                *lazadaMMOrderCreator
}

func newTestSetup(t *testing.T) *testSetup {
	ctrl := gomock.NewController(t)
	mockOrderCreator := mocks.NewMockOrderCreator(ctrl)
	mockParcelRepo := repoMocks.NewMockParcelRepository(ctrl)
	mockOrderRequestRepo := repoMocks.NewMockOrderRequestRepository(ctrl)
	mockShipmentParcelRepo := repoMocks.NewMockShipmentParcelRepository(ctrl)
	mockParcelItemRepo := repoMocks.NewMockParcelItemRepository(ctrl)

	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock DB: %v", err)
	}
	boil.SetDB(db)

	creator := &lazadaMMOrderCreator{
		orderCreator:       mockOrderCreator,
		parcelRepo:         mockParcelRepo,
		orderRequestRepo:   mockOrderRequestRepo,
		shipmentParcelRepo: mockShipmentParcelRepo,
		parcelItemsRepo:    mockParcelItemRepo,
	}

	return &testSetup{
		ctrl:                   ctrl,
		mockOrderCreator:       mockOrderCreator,
		mockParcelRepo:         mockParcelRepo,
		mockOrderRequestRepo:   mockOrderRequestRepo,
		mockShipmentParcelRepo: mockShipmentParcelRepo,
		mockParcelItemRepo:     mockParcelItemRepo,
		db:                     db,
		mock:                   mock,
		creator:                creator,
	}
}

func (s *testSetup) cleanup() {
	s.ctrl.Finish()
	s.db.Close()
}

func createTestLazadaRequest(sourceOrderID, refTrackingID, shipperRefNo string, parcelList []*order.LazadaMMParcelID) *order.LazadaMMCreateBagRequest {
	parcelListRequest := parcelList
	if parcelList == nil {
		parcelListRequest = []*order.LazadaMMParcelID{{
			LogisticsOrderCode: refTrackingID,
			TrackingNumber:     shipperRefNo,
		}}
	}
	return &order.LazadaMMCreateBagRequest{
		BigBagID:   sourceOrderID,
		LaneCode:   "SZXABKI-FLASH-STD-02",
		ParcelList: parcelListRequest,
	}
}

func TestLazadaMMOrderCreator_CreateMMCCBag(t *testing.T) {
	const (
		sourceOrderID = "test-source-order-id"
		refTrackingID = "parcel_number_test"
		shipperRefNo  = "TRK1234"
		partnerID     = 1
	)

	ctx := context.Background()
	partner := &models.Partner{ID: partnerID}
	expectedBag := &models.Parcel{ID: 123}

	t.Run("successful creation - created parcels before", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		lazRequest := createTestLazadaRequest(sourceOrderID, refTrackingID, shipperRefNo, nil)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(),
				[]string{refTrackingID},
				partner.ID).
			Return(models.ParcelSlice{{
				ID:            222,
				RefTrackingID: null.StringFrom(refTrackingID),
				Metadata:      null.StringFrom("{\"shipper_ref_no\":\"" + shipperRefNo + "\"}"),
			}}, nil)

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), &models.UniqueRequestID{
				RequestID: sourceOrderID,
				IDScheme:  configs.RequestIdSchemeLazadaMMOC.String(),
			}, gomock.Any()).
			Return(nil)

		s.mockOrderCreator.EXPECT().
			CreateMMCCOrder(gomock.Any(), gomock.Any(), partner, gomock.Any()).
			Return(expectedBag, nil)

		s.mockParcelRepo.EXPECT().
			BulkUpdateTxn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		bag, err := s.creator.CreateMMCCBag(ctx, partner, lazRequest)

		assert.NoError(t, err)
		assert.Equal(t, expectedBag, bag)
	})

	t.Run("successful creation - created parcels after", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		lazRequest := createTestLazadaRequest(sourceOrderID, refTrackingID, shipperRefNo, nil)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(),
				[]string{refTrackingID},
				partner.ID).
			Return(nil, nil)

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), &models.UniqueRequestID{
				RequestID: sourceOrderID,
				IDScheme:  configs.RequestIdSchemeLazadaMMOC.String(),
			}, gomock.Any()).
			Return(nil)

		s.mockOrderCreator.EXPECT().
			CreateMMCCOrder(gomock.Any(), gomock.Any(), partner, gomock.Any()).
			Return(expectedBag, nil)

		bag, err := s.creator.CreateMMCCBag(ctx, partner, lazRequest)

		assert.NoError(t, err)
		assert.Equal(t, expectedBag, bag)
	})

	t.Run("duplicate request - existing bag found", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		lazRequest := createTestLazadaRequest(sourceOrderID, refTrackingID, shipperRefNo, nil)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(),
				[]string{refTrackingID},
				partner.ID).
			Return(nil, nil)

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(errors.New("duplicate entry"))

		s.mockParcelRepo.EXPECT().
			GetOneByIdentifier(gomock.Any(), gomock.Any()).
			Return(expectedBag, nil)

		bag, err := s.creator.CreateMMCCBag(ctx, partner, lazRequest)

		assert.NoError(t, err)
		assert.Equal(t, expectedBag, bag)
	})

	t.Run("duplicate request - no existing bag", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		lazRequest := createTestLazadaRequest(sourceOrderID, refTrackingID, shipperRefNo, nil)

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(errors.New("duplicate entry"))

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(),
				[]string{refTrackingID},
				partner.ID).
			Return(nil, nil)
		s.mockParcelRepo.EXPECT().
			GetOneByIdentifier(gomock.Any(), gomock.Any()).
			Return(nil, nil)

		bag, err := s.creator.CreateMMCCBag(ctx, partner, lazRequest)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.ErrorIs(t, err, fplerror.ErrDuplicated)
	})

	t.Run("order creation failure", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		lazRequest := createTestLazadaRequest(sourceOrderID, refTrackingID, shipperRefNo, nil)
		baseError := errors.New("creation failed")
		expectedError := fmt.Errorf("can_not_create_bag: %w: %v", fplerror.ErrInternal, baseError)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(),
				[]string{refTrackingID},
				partner.ID).
			Return(nil, nil)

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), &models.UniqueRequestID{
				RequestID: sourceOrderID,
				IDScheme:  configs.RequestIdSchemeLazadaMMOC.String(),
			}, gomock.Any()).
			Return(nil)

		s.mockOrderCreator.EXPECT().
			CreateMMCCOrder(gomock.Any(), gomock.Any(), partner, gomock.Any()).
			Return(nil, baseError)

		bag, err := s.creator.CreateMMCCBag(ctx, partner, lazRequest)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Equal(t, expectedError, err)
	})

	t.Run("validation failure - parcels belong to other bags", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		lazRequest := createTestLazadaRequest(sourceOrderID, refTrackingID, shipperRefNo, nil)
		expectedError := fplerror.ErrParcelAssignedToBag

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{refTrackingID}, partner.ID).
			Return([]*models.Parcel{{
				ID:            222,
				RefTrackingID: null.StringFrom(refTrackingID),
				SourceOrderID: null.StringFrom("SOURCE_ORDER_ID_2"),
				Metadata:      null.StringFrom("{\"shipper_ref_no\":\"" + shipperRefNo + "\"}"),
			}}, nil)

		bag, err := s.creator.CreateMMCCBag(ctx, partner, lazRequest)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Equal(t, expectedError, err)
	})
}

func Test_lazadaMMOrderCreator_CreateMMCCParcel(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockOrderCreator := mocks.NewMockOrderCreator(ctrl)
	mockParcelRepo := repoMocks.NewMockParcelRepository(ctrl)

	creator := &lazadaMMOrderCreator{
		orderCreator: mockOrderCreator,
		parcelRepo:   mockParcelRepo,
	}

	ctx := context.Background()
	partner := &models.Partner{ID: 123}
	bigBagID := "BAG123"
	parcelReq := &order.BaseRequest{
		RefTrackingID: null.StringFrom("TRACK123").Ptr(),
		SourceOrderID: &bigBagID,
		ParcelDetails: &models.ParcelDetails{
			ShipperRefNo: "REF123",
		},
	}

	tests := []struct {
		name    string
		setup   func()
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "success - bag exists - create new parcel",
			setup: func() {
				// getBag success
				mockParcelRepo.EXPECT().
					GetOneByIdentifier(gomock.Any(), repositories.NewMmccBagIdentifier(partner.ID, bigBagID)).
					Return(&models.Parcel{ID: 1}, nil)

				// getExistingParcel returns no parcel
				mockParcelRepo.EXPECT().
					GetListWithCtx(gomock.Any(),
						models.ParcelWhere.RefTrackingID.EQ(null.StringFromPtr(parcelReq.RefTrackingID)),
						models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
					).
					Return(nil, nil)

				// createNewParcel success
				mockOrderCreator.EXPECT().
					CreateMMCCParcel(gomock.Any(), &order_creation_interface.CreateMMCCParcelPayload{
						Bag: &models.Parcel{ID: 1},
						MMCCParcel: &order_creation_interface.MMCCParcel{
							Parcel:      parcelReq.ToParcel(partner.ID),
							ParcelItems: parcelReq.ToParcelItems(),
						},
						MoveBagOnDuplicated: true,
					}).
					Return(nil)
			},
			wantErr: assert.NoError,
		},
		{
			name: "error - get bag database error",
			setup: func() {
				mockParcelRepo.EXPECT().
					GetOneByIdentifier(gomock.Any(), repositories.NewMmccBagIdentifier(partner.ID, bigBagID)).
					Return(nil, errors.New("database error"))
			},
			wantErr: func(t assert.TestingT, err error, msgAndArgs ...interface{}) bool {
				return assert.ErrorContains(t, err, "get bag error")
			},
		},
		{
			name: "error - get existing parcel database error",
			setup: func() {
				mockParcelRepo.EXPECT().
					GetOneByIdentifier(gomock.Any(), repositories.NewMmccBagIdentifier(partner.ID, bigBagID)).
					Return(&models.Parcel{ID: 1}, nil)

				mockParcelRepo.EXPECT().
					GetListWithCtx(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error"))
			},
			wantErr: func(t assert.TestingT, err error, msgAndArgs ...interface{}) bool {
				return assert.ErrorContains(t, err, "get parcel error")
			},
		},
		{
			name: "success - update existing parcel with existed bag",
			setup: func() {
				existingParcel := &models.Parcel{
					ID: 2,
					Metadata: null.StringFrom(
						utils.JsonMarshalStrIgnoreError(&models.ParcelDetails{
							ShipperRefNo: "REF123",
						}),
					),
				}
				bag := &models.Parcel{ID: 1}

				mockParcelRepo.EXPECT().
					GetOneByIdentifier(gomock.Any(), repositories.NewMmccBagIdentifier(partner.ID, bigBagID)).
					Return(bag, nil)

				mockParcelRepo.EXPECT().
					GetListWithCtx(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(models.ParcelSlice{existingParcel}, nil)

				mockParcelRepo.EXPECT().
					UpdateWithWhitelistTxn(
						gomock.Any(),
						existingParcel,
						boil.Whitelist([]string{
							models.ParcelColumns.SourceOrderID,
							models.ParcelColumns.ParentID,
							models.ParcelColumns.ServiceID,
							models.ParcelColumns.ProductID,
							models.ParcelColumns.OriginVendorID,
							models.ParcelColumns.PartnerID,
							models.ParcelColumns.ShipperID,
						}...),
						gomock.Any(),
					).
					Return(nil)
			},
			wantErr: assert.NoError,
		},
		{
			name: "success - update existing parcel with no existed bag",
			setup: func() {
				existingParcel := &models.Parcel{
					ID: 2,
					Metadata: null.StringFrom(
						utils.JsonMarshalStrIgnoreError(&models.ParcelDetails{
							ShipperRefNo: "REF123",
						}),
					),
				}

				mockParcelRepo.EXPECT().
					GetOneByIdentifier(gomock.Any(), repositories.NewMmccBagIdentifier(partner.ID, bigBagID)).
					Return(nil, nil)

				mockParcelRepo.EXPECT().
					GetListWithCtx(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(models.ParcelSlice{existingParcel}, nil)
				existingParcel.SourceOrderID = null.StringFromPtr(parcelReq.SourceOrderID)

				mockParcelRepo.EXPECT().
					UpdateWithWhitelistTxn(
						gomock.Any(),
						existingParcel,
						boil.Whitelist([]string{
							models.ParcelColumns.SourceOrderID,
						}...),
						gomock.Any(),
					).
					Return(nil)
			},
			wantErr: assert.NoError,
		},
		{
			name: "error - create new parcel fails",
			setup: func() {
				mockParcelRepo.EXPECT().
					GetOneByIdentifier(gomock.Any(), repositories.NewMmccBagIdentifier(partner.ID, bigBagID)).
					Return(&models.Parcel{ID: 1}, nil)

				mockParcelRepo.EXPECT().
					GetListWithCtx(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, sql.ErrNoRows)

				mockOrderCreator.EXPECT().
					CreateMMCCParcel(gomock.Any(), gomock.Any()).
					Return(errors.New("create parcel error"))
			},
			wantErr: func(t assert.TestingT, err error, msgAndArgs ...interface{}) bool {
				return assert.ErrorContains(t, err, "create lazada mm parcel error")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			err := creator.CreateMMCCParcel(ctx, partner, bigBagID, parcelReq)
			tt.wantErr(t, err)
		})
	}
}

func TestLazadaMMOrderCreator_UpdateMMCCBag(t *testing.T) {
	testCtx := context.TODO()
	testPartner := &models.Partner{ID: 1}
	testReq := &order.LazadaMMUpdateRequest{
		BigBagID: "bag123",
		ParcelList: []*order.LazadaMMParcelID{
			{LogisticsOrderCode: "order1", TrackingNumber: "track1"},
			{LogisticsOrderCode: "order2", TrackingNumber: "track2"},
		},
	}

	testParcels := models.ParcelSlice{
		{ID: 1, RefTrackingID: null.StringFrom("order1"), Metadata: null.StringFrom(`{"shipper_ref_no":"track1"}`), SourceOrderID: null.StringFrom("bag123")},
		{ID: 2, RefTrackingID: null.StringFrom("order2"), Metadata: null.StringFrom(`{"shipper_ref_no":"track2"}`), SourceOrderID: null.StringFrom("bag123")},
	}

	testShipmentParcels := models.ShipmentParcelSlice{
		{ParcelID: null.UintFrom(1)},
	}

	t.Run("success", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()

		// Mock validation
		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(testCtx, []string{"order1", "order2"}, uint64(1)).
			Return(testParcels, nil)

		// Mock getting shipment relationships
		s.mockShipmentParcelRepo.EXPECT().
			GetShipmentIDsByParcelIDs(testCtx, []uint{1, 2}).
			Return(testShipmentParcels, nil)

		// Mock transaction
		s.mockParcelRepo.EXPECT().
			BulkDeleteByIDsTxn(testCtx, gomock.Any(), []uint{2}).
			Return(nil)

		s.mockParcelItemRepo.EXPECT().
			DeleteAllWithTxn(testCtx, gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			BulkUpdateTxn(
				testCtx,
				gomock.Any(),
				models.M{
					models.ParcelColumns.ParentID:      null.NewUint(0, false),
					models.ParcelColumns.SourceOrderID: null.NewString("", false),
				},
				gomock.Any(),
			).
			Return(nil)
		s.mock.ExpectCommit()
		err := s.creator.UpdateMMCCBag(testCtx, testPartner, testReq)
		assert.NoError(t, err)
	})

	t.Run("no parcels found - early return", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(testCtx, []string{"order1", "order2"}, uint64(1)).
			Return(models.ParcelSlice{}, nil)

		err := s.creator.UpdateMMCCBag(testCtx, testPartner, testReq)
		assert.NoError(t, err)
	})

	t.Run("validation error", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		expectedErr := errors.New("validation failed")
		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(testCtx, []string{"order1", "order2"}, uint64(1)).
			Return(nil, expectedErr)

		err := s.creator.UpdateMMCCBag(testCtx, testPartner, testReq)
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestLazadaMMOrderCreator_CreateMMCCParcelAIDC(t *testing.T) {
	t.Parallel()

	ctx := context.TODO()
	partner := &models.Partner{ID: 123}
	req := &order.BaseRequest{
		RequestId:        "test-request-id",
		PartnerUniqueKey: lo.ToPtr("test-unique-key"),
	}

	t.Run("success", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()
		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		uniqueID := &models.UniqueRequestID{
			RequestID: req.RequestId,
			IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
		}

		s.mockOrderRequestRepo.EXPECT().CreateUniqueRequestIdTxn(gomock.Any(), gomock.Eq(uniqueID), gomock.Any()).Return(nil)
		s.mockOrderCreator.EXPECT().CreateMMCCParcel(gomock.Any(), gomock.Any()).Return(nil)

		err := s.creator.CreateMMCCParcelAIDC(ctx, partner, req)
		assert.NoError(t, err)
	})

	t.Run("duplicate_request_id_with_existing_parcel", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()
		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		uniqueID := &models.UniqueRequestID{
			RequestID: req.RequestId,
			IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
		}

		s.mockOrderRequestRepo.EXPECT().CreateUniqueRequestIdTxn(gomock.Any(), gomock.Eq(uniqueID), gomock.Any()).Return(errors.New("duplicate"))
		s.mockParcelRepo.EXPECT().GetOneByIdentifier(gomock.Any(), gomock.Any()).Return(&models.Parcel{ID: 456}, nil)

		err := s.creator.CreateMMCCParcelAIDC(ctx, partner, req)
		assert.NoError(t, err)
	})

	t.Run("duplicate_request_id_without_existing_parcel", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()
		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		uniqueID := &models.UniqueRequestID{
			RequestID: req.RequestId,
			IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
		}

		s.mockOrderRequestRepo.EXPECT().CreateUniqueRequestIdTxn(gomock.Any(), gomock.Eq(uniqueID), gomock.Any()).Return(errors.New("duplicate"))
		s.mockParcelRepo.EXPECT().GetOneByIdentifier(gomock.Any(), gomock.Any()).Return(nil, errors.New("not found"))

		err := s.creator.CreateMMCCParcelAIDC(ctx, partner, req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "duplicated_order_request_id")
	})

	t.Run("create_mmcc_parcel_error", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()
		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		uniqueID := &models.UniqueRequestID{
			RequestID: req.RequestId,
			IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
		}

		s.mockOrderRequestRepo.EXPECT().CreateUniqueRequestIdTxn(gomock.Any(), gomock.Eq(uniqueID), gomock.Any()).Return(nil)
		s.mockOrderCreator.EXPECT().CreateMMCCParcel(gomock.Any(), gomock.Any()).Return(errors.New("creation error"))

		err := s.creator.CreateMMCCParcelAIDC(ctx, partner, req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "create lazada mm parcel error")
	})
}

func TestLazadaMMOrderCreator_CreateAIDCMMCCBag(t *testing.T) {
	const (
		bigBagID     = "test-big-bag-id"
		partnerID    = 1
		idempotentID = "test-idempotent-id"
		orderCode    = "test-order-code"
		trackingNo   = "test-tracking-no"
	)

	ctx := context.Background()
	partner := &models.Partner{ID: partnerID}
	expectedBag := &models.Parcel{ID: 123}

	t.Run("successful creation", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		// Mock unique request ID creation
		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), &models.UniqueRequestID{
				RequestID: idempotentID,
				IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
			}, gomock.Any()).
			Return(nil)

		// Mock check for existing bag
		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		// Mock parcel validation
		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		// Mock additional parcel validation
		s.mockParcelRepo.EXPECT().
			GetListWithCtx(gomock.Any(),
				models.ParcelWhere.PartnerUniqueKey.IN([]string{fmt.Sprintf("%s_%s", orderCode, trackingNo)}),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
			).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		// Mock order creation
		s.mockOrderCreator.EXPECT().
			CreateMMCCOrder(gomock.Any(), gomock.Any(), partner, gomock.Any()).
			Return(expectedBag, nil)

		// Mock parcel update
		s.mockParcelRepo.EXPECT().
			BulkUpdateTxn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.NoError(t, err)
		assert.Equal(t, expectedBag, bag)
	})

	t.Run("validation failure - parcels assigned to another bag", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				ParentID:         null.UintFrom(333),
				SourceOrderID:    null.StringFrom("other-bag"),
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		s.mockParcelRepo.EXPECT().
			GetListWithCtx(gomock.Any(),
				models.ParcelWhere.PartnerUniqueKey.IN([]string{fmt.Sprintf("%s_%s", orderCode, trackingNo)}),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
			).
			Return(models.ParcelSlice{{
				ID:               222,
				ParentID:         null.UintFrom(333),
				SourceOrderID:    null.StringFrom("other-bag"),
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.ErrorIs(t, err, fplerror.ErrParcelAssignedToBag)
	})

	t.Run("validation failure - invalid cargo type", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    999,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		s.mockParcelRepo.EXPECT().
			GetListWithCtx(gomock.Any(),
				models.ParcelWhere.PartnerUniqueKey.IN([]string{fmt.Sprintf("%s_%s", orderCode, trackingNo)}),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
			).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unavailable service by cargo_type")
	})

	t.Run("validation failure - invalid to location", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "INVALID",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		s.mockParcelRepo.EXPECT().
			GetListWithCtx(gomock.Any(),
				models.ParcelWhere.PartnerUniqueKey.IN([]string{fmt.Sprintf("%s_%s", orderCode, trackingNo)}),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
			).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unavailable service by to_location")
	})

	t.Run("bag creation failure", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		s.mockParcelRepo.EXPECT().
			GetListWithCtx(gomock.Any(),
				models.ParcelWhere.PartnerUniqueKey.IN([]string{fmt.Sprintf("%s_%s", orderCode, trackingNo)}),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
			).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		s.mockOrderCreator.EXPECT().
			CreateMMCCOrder(gomock.Any(), gomock.Any(), partner, gomock.Any()).
			Return(nil, errors.New("creation failed"))

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "can_not_create_bag")
	})

	t.Run("parcel update failure", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		s.mockParcelRepo.EXPECT().
			GetListWithCtx(gomock.Any(),
				models.ParcelWhere.PartnerUniqueKey.IN([]string{fmt.Sprintf("%s_%s", orderCode, trackingNo)}),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
			).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		s.mockOrderCreator.EXPECT().
			CreateMMCCOrder(gomock.Any(), gomock.Any(), partner, gomock.Any()).
			Return(expectedBag, nil)

		s.mockParcelRepo.EXPECT().
			BulkUpdateTxn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(errors.New("update failed"))

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed_to_link_bag_and_parcels")
	})

	t.Run("transaction begin failure", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		// Override the mock to simulate transaction begin failure
		s.mock.ExpectBegin().WillReturnError(errors.New("transaction begin failed"))

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "transaction begin failed")
	})

	t.Run("duplicate request with existing bag found", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectCommit()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		// Mock duplicate request ID creation failure
		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), &models.UniqueRequestID{
				RequestID: idempotentID,
				IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
			}, gomock.Any()).
			Return(errors.New("duplicate entry"))

		// Mock bag retrieval - bag exists
		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(expectedBag, nil)

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.NoError(t, err)
		assert.Equal(t, expectedBag, bag)
	})

	t.Run("duplicate request with bag retrieval error", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		// Mock duplicate request ID creation failure
		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), &models.UniqueRequestID{
				RequestID: idempotentID,
				IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
			}, gomock.Any()).
			Return(errors.New("duplicate entry"))

		// Mock bag retrieval - database error
		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, errors.New("database error"))

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed_to_get_bag")
	})

	t.Run("duplicate bag error - bag already exists", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		// Mock unique request ID creation success (no duplicate)
		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), &models.UniqueRequestID{
				RequestID: idempotentID,
				IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
			}, gomock.Any()).
			Return(nil)

		// Mock bag retrieval - bag already exists
		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(expectedBag, nil)

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "duplicated_bag")
		assert.Contains(t, err.Error(), bigBagID)
	})

	t.Run("parcel validation error - database error getting parcels by RefTIDs", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		// Mock parcel validation - database error
		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(nil, errors.New("database error"))

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("parcel validation error - not all parcels created", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
				{
					OrderCode:      "order-code-2",
					TrackingNumber: "tracking-no-2",
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		// Mock parcel validation - only one parcel found but two requested
		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode, "order-code-2"}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not all parcels created")
		assert.ErrorIs(t, err, fplerror.ErrParcelNotCreated)
	})

	t.Run("parcel validation error - database error getting parcels by PartnerUniqueKey", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		// Mock first parcel validation - success
		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		// Mock second parcel validation - database error
		s.mockParcelRepo.EXPECT().
			GetListWithCtx(gomock.Any(),
				models.ParcelWhere.PartnerUniqueKey.IN([]string{fmt.Sprintf("%s_%s", orderCode, trackingNo)}),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
			).
			Return(nil, errors.New("database error"))

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get parcels inside bag")
		assert.ErrorIs(t, err, fplerror.ErrInternal)
	})

	t.Run("parcel validation error - no parcels created yet", func(t *testing.T) {
		s := newTestSetup(t)
		defer s.cleanup()

		s.mock.ExpectBegin()
		s.mock.ExpectRollback()

		req := &order.LazadaAIDCMMCreateBagRequest{
			BigBagID:     bigBagID,
			IdempotentId: idempotentID,
			CargoType:    2,
			ToLocation:   "BKI",
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      orderCode,
					TrackingNumber: trackingNo,
				},
			},
		}

		s.mockOrderRequestRepo.EXPECT().
			CreateUniqueRequestIdTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		s.mockParcelRepo.EXPECT().
			GetOne(gomock.Any(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
				models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
			).
			Return(nil, nil)

		// Mock first parcel validation - success
		s.mockParcelRepo.EXPECT().
			GetMMCCParcelsByRefTIDsAndPartnerID(gomock.Any(), []string{orderCode}, partner.ID).
			Return(models.ParcelSlice{{
				ID:               222,
				PartnerUniqueKey: null.StringFrom(fmt.Sprintf("%s_%s", orderCode, trackingNo)),
			}}, nil)

		// Mock second parcel validation - no parcels found
		s.mockParcelRepo.EXPECT().
			GetListWithCtx(gomock.Any(),
				models.ParcelWhere.PartnerUniqueKey.IN([]string{fmt.Sprintf("%s_%s", orderCode, trackingNo)}),
				models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
			).
			Return(models.ParcelSlice{}, nil)

		bag, err := s.creator.CreateAIDCMMCCBag(ctx, partner, req)

		assert.Nil(t, bag)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no parcels created yet")
		assert.ErrorIs(t, err, fplerror.ErrParcelNotCreated)
	})
}
