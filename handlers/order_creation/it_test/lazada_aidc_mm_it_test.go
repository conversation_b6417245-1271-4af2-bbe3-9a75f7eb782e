go:build integration
+build integration

package it_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"

	parcelConst "git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/testutils"
)

func (s *ITLazadaMMOrderCreator) TestLazadaAIDCMMOrderCreator_CreateMMCCParcel() {
	const (
		partnerID = 88426
	)

	partner, err := repositories.NewPartnerRepository().
		GetOne(context.TODO(), models.PartnerWhere.ID.EQ(partnerID))
	if err != nil {
		s.T().Error(err)
		return
	}

	s.T().Run("Create mmcc parcel with valid request", func(t *testing.T) {
		mmccParcel := &order.BaseRequest{
			RequestId: uuid.New().String(),
			From: order.Address{
				Name:          "Myint Maung",
				AddressLine1:  "No.171, Market Street, Southern District, Naypyidaw",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Mandalay Region"),
				CountryCode:   "SG",
				PostCode:      lo.ToPtr(""),
				ContactNumber: lo.ToPtr("+95831015052"),
				ContactEmail:  lo.ToPtr(""),
			},
			To: order.Address{
				Name:          "Myint Thet",
				AddressLine1:  "No.361, Palace Road, Southern District, Yangon",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Bago Region"),
				CountryCode:   "PH",
				PostCode:      lo.ToPtr("297311"),
				ContactNumber: lo.ToPtr("+95090617001"),
				ContactEmail:  lo.ToPtr("<EMAIL>"),
			},
			ParcelDetails: &models.ParcelDetails{
				ShipperSubmittedWeight: lo.ToPtr(9.571673482077689),
				Weight:                 lo.ToPtr(9.571673482077689),
				Value:                  lo.ToPtr(905.************),
				CustomsCurrency:        lo.ToPtr("USD"),
				Quantity:               lo.ToPtr[uint](2),
				ShipperSubmittedDimensions: &models.Dimensions{
					Length: lo.ToPtr(41.47050153553845),
					Width:  lo.ToPtr(8.327161496651012),
					Height: lo.ToPtr(49.011430382809415),
				},
			},
			Items: order.ParcelItemSlice{
				{
					Description: lo.ToPtr("Item 1"),
					UnitValue:   lo.ToPtr(201.1),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.1),
				},
				{
					Description: lo.ToPtr("Item 2"),
					UnitValue:   lo.ToPtr(201.2),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.2),
				},
			},
			PartnerUniqueKey: lo.ToPtr("LZDCC-PARCEL-UNIQ1747033508893"),
		}

		err = s.creator.CreateMMCCParcelAIDC(context.Background(), partner, mmccParcel)
		if err != nil {
			t.Error(err)
			return
		}

		p, err := repositories.NewParcelRepository().GetOneByIdentifier(context.Background(), repositories.NewMMCCParcelIdentifier(partnerID, *mmccParcel.PartnerUniqueKey))
		if err != nil {
			t.Error(err)
			return
		}

		tobeParcel := models.Parcel{
			PartnerID:         null.Uint64From(partnerID),
			Type:              parcelConst.MMCCParcel,
			FromName:          null.StringFrom("Myint Maung"),
			FromAddressLine1:  null.StringFrom("No.171, Market Street, Southern District, Naypyidaw"),
			FromCity:          null.StringFrom("Mawlamyine"),
			FromStateProvince: null.StringFrom("Mandalay Region"),
			FromCountryCode:   null.StringFrom("SG"),
			FromPostcode:      null.StringFrom(""),
			FromContactNumber: null.StringFrom("+95831015052"),
			ToName:            null.StringFrom("Myint Thet"),
			ToAddressLine1:    null.StringFrom("No.361, Palace Road, Southern District, Yangon"),
			ToCity:            null.StringFrom("Mawlamyine"),
			ToStateProvince:   null.StringFrom("Bago Region"),
			ToCountryCode:     null.StringFrom("PH"),
			ToPostcode:        null.StringFrom("297311"),
		}
		if !testutils.CompareNonEmptyFields(p, tobeParcel) {
			t.Error(fmt.Errorf("Expected parcel to be created %+v, but got %+v", tobeParcel, p))
			return
		}
	})

	s.T().Run("Create mmcc parcel with the same idempotent ID", func(t *testing.T) {
		// Reuse the same parcel data from the previous test
		mmccParcel := &order.BaseRequest{
			RequestId: uuid.New().String(),
			From: order.Address{
				Name:          "Myint Maung",
				AddressLine1:  "No.171, Market Street, Southern District, Naypyidaw",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Mandalay Region"),
				CountryCode:   "SG",
				PostCode:      lo.ToPtr(""),
				ContactNumber: lo.ToPtr("+95831015052"),
				ContactEmail:  lo.ToPtr(""),
			},
			To: order.Address{
				Name:          "Myint Thet",
				AddressLine1:  "No.361, Palace Road, Southern District, Yangon",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Bago Region"),
				CountryCode:   "PH",
				PostCode:      lo.ToPtr("297311"),
				ContactNumber: lo.ToPtr("+95090617001"),
				ContactEmail:  lo.ToPtr("<EMAIL>"),
			},
			ParcelDetails: &models.ParcelDetails{
				ShipperSubmittedWeight: lo.ToPtr(9.571673482077689),
				Weight:                 lo.ToPtr(9.571673482077689),
				Value:                  lo.ToPtr(905.************),
				CustomsCurrency:        lo.ToPtr("USD"),
				Quantity:               lo.ToPtr[uint](2),
				ShipperSubmittedDimensions: &models.Dimensions{
					Length: lo.ToPtr(41.47050153553845),
					Width:  lo.ToPtr(8.327161496651012),
					Height: lo.ToPtr(49.011430382809415),
				},
			},
			Items: order.ParcelItemSlice{
				{
					Description: lo.ToPtr("Item 1"),
					UnitValue:   lo.ToPtr(201.1),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.1),
				},
				{
					Description: lo.ToPtr("Item 2"),
					UnitValue:   lo.ToPtr(201.2),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.2),
				},
			},
			PartnerUniqueKey: lo.ToPtr(uuid.New().String()),
		}

		// First creation attempt
		err = s.creator.CreateMMCCParcelAIDC(context.Background(), partner, mmccParcel)
		if err != nil {
			t.Error(err)
			return
		}

		// Get the first parcel
		firstParcel, err := repositories.NewParcelRepository().GetOneByIdentifier(context.Background(), repositories.NewMMCCParcelIdentifier(partnerID, *mmccParcel.PartnerUniqueKey))
		if err != nil {
			t.Error(err)
			return
		}

		// Second creation attempt with the same data
		err = s.creator.CreateMMCCParcelAIDC(context.Background(), partner, mmccParcel)
		if err != nil {
			t.Error(err)
			return
		}

		// Get the parcel after second attempt
		secondParcel, err := repositories.NewParcelRepository().GetOneByIdentifier(context.Background(), repositories.NewMMCCParcelIdentifier(partnerID, *mmccParcel.PartnerUniqueKey))
		if err != nil {
			t.Error(err)
			return
		}

		// Verify that both parcels are the same (idempotency)
		if firstParcel.ID != secondParcel.ID {
			t.Error(fmt.Errorf("Expected idempotent creation to return the same parcel, but got different IDs: %d vs %d", firstParcel.ID, secondParcel.ID))
			return
		}
	})
}
