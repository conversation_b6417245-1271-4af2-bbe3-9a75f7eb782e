package order_creation

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvtracer"

	"git.ninjavan.co/3pl/configs"
	"git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/order_creation/order_creation_interface"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

type LazadaMMOrderCreator interface {
	CreateMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaMMCreateBagRequest) (*models.Parcel, error)
	UpdateMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaMMUpdateRequest) error
	CreateMMCCParcel(ctx context.Context, partner *models.Partner, bigBagID string, mmccParcel *order.BaseRequest) error

	CreateMMCCParcelAIDC(ctx context.Context, partner *models.Partner, mmccParcel *order.BaseRequest) error
	CreateAIDCMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaAIDCMMCreateBagRequest) (*models.Parcel, error)
}

type lazadaMMOrderCreator struct {
	orderCreator       order_creation_interface.OrderCreator
	parcelRepo         repo_interface.ParcelRepository
	orderRequestRepo   repo_interface.OrderRequestRepository
	shipmentParcelRepo repo_interface.ShipmentParcelRepository
	parcelItemsRepo    repo_interface.ParcelItemRepository
}

func NewLazadaMMOrderCreator() *lazadaMMOrderCreator {
	return &lazadaMMOrderCreator{
		orderCreator:       NewOrderCreator(),
		parcelRepo:         repositories.NewParcelRepository(),
		orderRequestRepo:   repositories.NewOrderRequestRepository(),
		shipmentParcelRepo: repositories.NewShipmentParcelRepository(),
		parcelItemsRepo:    repositories.NewParcelItemRepository(),
	}
}

func (l *lazadaMMOrderCreator) CreateMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaMMCreateBagRequest) (*models.Parcel, error) {
	ctx, span := nvtracer.CreateSpanFromContext(ctx)
	defer span.Finish()

	unlinkParcels, err := l.validateParcelsInsideBag(ctx, partner, req)
	if err != nil {
		return nil, err
	}

	bagReq, err := convertLazadaMMOCReq(req)
	if err != nil {
		return nil, err
	}

	orderReqUniqueId := &models.UniqueRequestID{
		RequestID: bagReq.RequestId,
		IDScheme:  configs.RequestIdSchemeLazadaMMOC.String(),
	}

	txn, err := repositories.BeginTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		err = repositories.ProcessTransaction(txn, err)
		if err != nil {
			loggerutils.Ctx(ctx, "lazada-mm-order-creator").Err(err).Msg("fail to process txn")
		}
	}()
	err = l.orderRequestRepo.CreateUniqueRequestIdTxn(ctx, orderReqUniqueId, txn)
	if err != nil {
		bag, _ := l.parcelRepo.GetOneByIdentifier(ctx, repositories.NewMmccBagIdentifier(partner.ID, *bagReq.SourceOrderID))
		if bag != nil {
			return bag, nil
		}
		return nil, fmt.Errorf("duplicated_order_request_id: %w: %v", fplerror.ErrDuplicated, err)
	}

	bag, err := l.orderCreator.CreateMMCCOrder(ctx, &order_creation_interface.MMCCOCPayload{
		BagRequest:  bagReq,
		MMCCParcels: nil,
	}, partner, txn)
	if err != nil {
		return nil, fmt.Errorf("can_not_create_bag: %w: %v", fplerror.ErrInternal, err)
	}

	if len(unlinkParcels) == 0 {
		return bag, nil
	}

	err = l.parcelRepo.BulkUpdateTxn(ctx, unlinkParcels,
		models.M{
			models.ParcelColumns.ServiceID:      bag.ServiceID,
			models.ParcelColumns.ProductID:      bag.ProductID,
			models.ParcelColumns.ShipperID:      bag.ShipperID,
			models.ParcelColumns.ParentID:       bag.ID,
			models.ParcelColumns.SourceOrderID:  bag.SourceOrderID,
			models.ParcelColumns.OriginVendorID: bag.OriginVendorID,
		},
		txn,
	)

	if err != nil {
		return nil, fmt.Errorf("failed_to_link_bag_and_parcels %w", err)
	}
	return bag, nil
}

func (l *lazadaMMOrderCreator) UpdateMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaMMUpdateRequest) error {
	parcels, err := l.buzValidationForUpdateBag(ctx, partner, req)
	if err != nil {
		return err
	}
	if len(parcels) == 0 {
		return nil
	}

	sps, err := l.shipmentParcelRepo.GetShipmentIDsByParcelIDs(ctx, lo.Map(parcels, func(p *models.Parcel, index int) uint {
		return p.ID
	}))
	if err != nil {
		return err
	}
	parcelsInShipment := lo.SliceToMap(sps, func(sp *models.ShipmentParcel) (uint, struct{}) {
		return sp.ParcelID.Uint, struct{}{}
	})

	var tbHardDeleted []*models.Parcel
	var tbDelRels []*models.Parcel
	for _, p := range parcels {
		if _, ok := parcelsInShipment[p.ID]; ok {
			tbDelRels = append(tbDelRels, p)
		} else {
			tbHardDeleted = append(tbHardDeleted, p)
		}
	}

	txn, err := repositories.BeginTransaction(ctx)
	if err != nil {
		return err
	}

	defer func() {
		err = repositories.ProcessTransaction(txn, err)
		if err != nil {
			loggerutils.Ctx(ctx, "lazada-mm-order-creator").Err(err).Msg("fail to delete parcels")
		}
	}()

	err = l.parcelRepo.BulkDeleteByIDsTxn(ctx, txn, lo.Map(tbHardDeleted, func(p *models.Parcel, index int) uint {
		return p.ID
	}))
	if err != nil {
		return err
	}

	delParcelItem := []qm.QueryMod{
		models.ParcelItemWhere.ParcelID.IN(lo.Map(tbHardDeleted, func(p *models.Parcel, _ int) uint64 {
			return uint64(p.ID)
		})),
	}
	err = l.parcelItemsRepo.DeleteAllWithTxn(ctx, txn, delParcelItem...)
	if err != nil {
		return err
	}

	err = l.parcelRepo.BulkUpdateTxn(ctx, tbDelRels, models.M{
		models.ParcelColumns.ParentID:      null.NewUint(0, false),
		models.ParcelColumns.SourceOrderID: null.NewString("", false),
	}, txn)
	if err != nil {
		return err
	}
	return nil
}

func (l *lazadaMMOrderCreator) buzValidationForUpdateBag(ctx context.Context, partner *models.Partner, req *order.LazadaMMUpdateRequest) (models.ParcelSlice, error) {
	refTIDs := lo.Map(req.ParcelList, func(p *order.LazadaMMParcelID, index int) string {
		return p.LogisticsOrderCode
	})
	parcels, err := l.parcelRepo.GetMMCCParcelsByRefTIDsAndPartnerID(ctx, refTIDs, partner.ID)
	if err != nil {
		return nil, err
	}

	lzdParcelIDs := lo.SliceToMap(req.ParcelList, func(parcel *order.LazadaMMParcelID) (string, struct{}) {
		return l.lazadaParcelID(parcel.LogisticsOrderCode, parcel.TrackingNumber), struct{}{}
	})

	parcels = lo.Filter(parcels, func(p *models.Parcel, index int) bool {
		var md repo_interface.ParcelMetadata
		_ = json.Unmarshal([]byte(p.Metadata.String), &md)
		id := l.lazadaParcelID(p.RefTrackingID.String, md.ShipperRefNo)
		_, ok := lzdParcelIDs[id]
		return ok
	})

	for _, p := range parcels {
		if p.SourceOrderID.String != req.BigBagID {
			return nil, fplerror.ErrBadRequest.Newf("the parcel logisticsOrderCode: %s is not belong to big bag %s", p.RefTrackingID.String, req.BigBagID)
		}
	}
	return parcels, nil
}

func (l *lazadaMMOrderCreator) lazadaParcelID(logisticOrderCode string, trackingNumber string) string {
	return fmt.Sprintf("%s:%s", logisticOrderCode, trackingNumber)
}

func (l *lazadaMMOrderCreator) CreateMMCCParcel(ctx context.Context, partner *models.Partner, bigBagID string, req *order.BaseRequest) error {
	bag, err := l.parcelRepo.GetOneByIdentifier(ctx, repositories.NewMmccBagIdentifier(partner.ID, bigBagID))
	if err != nil {
		return fmt.Errorf("get bag error: %w", err)
	}
	parcel, err := l.getParcel(ctx, partner.ID, req)
	if err != nil {
		return err
	}

	if parcel != nil {
		return l.updateExistedParcel(ctx, parcel, bag, req)
	}

	if err = l.orderCreator.CreateMMCCParcel(ctx, &order_creation_interface.CreateMMCCParcelPayload{
		Bag: bag,
		MMCCParcel: &order_creation_interface.MMCCParcel{
			Parcel:      req.ToParcel(partner.ID),
			ParcelItems: req.ToParcelItems(),
		},
		MoveBagOnDuplicated: true,
	}); err != nil {
		return fmt.Errorf("create lazada mm parcel error: %w", err)
	}

	return nil
}

func (l *lazadaMMOrderCreator) updateExistedParcel(ctx context.Context, parcel *models.Parcel,
	bag *models.Parcel, req *order.BaseRequest) error {
	if parcel.ParentID.Valid {
		if parcel.SourceOrderID.String == null.StringFromPtr(req.SourceOrderID).String {
			return nil
		}
		return fplerror.ErrBadRequest.Newf("parcel has been assign to other bag %d", parcel.ParentID.Uint)
	}

	whiteListColumn := []string{
		models.ParcelColumns.SourceOrderID,
	}
	if bag != nil {
		populateParcelWithBagInfo(bag, parcel)
		whiteListColumn = append(whiteListColumn,
			models.ParcelColumns.ParentID,
			models.ParcelColumns.ServiceID,
			models.ParcelColumns.ProductID,
			models.ParcelColumns.OriginVendorID,
			models.ParcelColumns.PartnerID,
			models.ParcelColumns.ShipperID,
		)
	} else {
		parcel.SourceOrderID = null.StringFromPtr(req.SourceOrderID)
	}

	return l.parcelRepo.UpdateWithWhitelistTxn(
		ctx, parcel, boil.Whitelist(whiteListColumn...),
		nil,
	)
}

func (l *lazadaMMOrderCreator) getParcel(ctx context.Context, partnerId uint64, req *order.BaseRequest) (*models.Parcel, error) {
	parcels, err := l.parcelRepo.GetListWithCtx(ctx,
		models.ParcelWhere.RefTrackingID.EQ(null.StringFromPtr(req.RefTrackingID)),
		models.ParcelWhere.PartnerID.EQ(null.Uint64From(partnerId)),
	)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, fmt.Errorf("get parcel error: %w", err)
	}
	var parcelFound *models.Parcel

	for _, parcel := range parcels {
		var parcelMetadata repo_interface.ParcelMetadata
		if err = json.Unmarshal([]byte(parcel.Metadata.String), &parcelMetadata); err != nil {
			return nil, fmt.Errorf("unmarshal parcel metadata: %w", err)
		}

		if parcelMetadata.ShipperRefNo == req.ParcelDetails.ShipperRefNo {
			parcelFound = parcel
			break
		}
	}

	return parcelFound, nil
}

var laneCodeToSC = map[string]string{
	"SZXABKI-FLASH-STD-02": "CNMY-A-S-BKI-AF-2",
	"SZXAKCH-FLASH-STD-02": "CNMY-A-S-KCH-AF-2",
	"SZXAKCH-JT-STD-02":    "CNMY-A-S-KCH-AF-2",
}

var cargoTypeAndToLocationToServiceCode = map[uint]map[string]string{
	2: {
		"BKI": "CNMY-A-S-BKI-AF-2",
		"KCH": "CNMY-A-S-KCH-AF-2",
	},
}

const (
	nvName            = "Ninjalogistics"
	fromAddressLine1  = "金港北三路6号"
	fromAddressLine2  = "龙地广州空港物流园"
	fromAddressLine3  = "P栋311"
	fromAddressLine4  = "空港花都"
	fromCity          = "广州市"
	fromStateProvince = "广东省"
	fromCountryCode   = "CN"
	fromPostCode      = "510801"

	toAddressLine1  = "Lot 1-8, Persiaran Jubli Perak"
	toAddressLine2  = "Seksyen 22"
	toCity          = "Shah Alam"
	toStateProvince = "Selangor Darul Ehsan"
	toCountryCode   = "MY"
	toPostCode      = "40300"

	dimensionUnitInCm = "cm"
)

func convertLazadaMMOCReq(request *order.LazadaMMCreateBagRequest) (*order.BaseRequest, error) {
	wu := request.WeightUnit
	gw := utils.ParseFloat64(null.StringFrom(request.GrossWeight)).Float64
	lw := utils.ParseFloat64(null.StringFrom(request.LabelWeight)).Float64
	if wu == "" || strings.EqualFold(wu, "g") {
		gw = gw / 1000
		lw = lw / 1000
		wu = "kg"
	}
	sc, ok := laneCodeToSC[request.LaneCode]
	if !ok {
		return nil, fplerror.ErrBadRequest.NewWithoutStack("can not find SC by lane code: %s", request.LaneCode)
	}
	bagReq := &order.BaseRequest{
		Source:        order.SourceCustomAPI,
		SourceOrderID: lo.ToPtr(request.BigBagID),
		RefTrackingID: lo.ToPtr(request.BigBagID),
		ServiceCode:   sc,
		From: order.Address{
			Name:          nvName,
			AddressLine1:  fromAddressLine1,
			AddressLine2:  lo.ToPtr(fromAddressLine2),
			AddressLine3:  lo.ToPtr(fromAddressLine3),
			AddressLine4:  lo.ToPtr(fromAddressLine4),
			City:          lo.ToPtr(fromCity),
			StateProvince: lo.ToPtr(fromStateProvince),
			CountryCode:   fromCountryCode,
			PostCode:      lo.ToPtr(fromPostCode),
		},
		To: order.Address{
			Name:          nvName,
			AddressLine1:  toAddressLine1,
			AddressLine2:  lo.ToPtr(toAddressLine2),
			City:          lo.ToPtr(toCity),
			StateProvince: lo.ToPtr(toStateProvince),
			CountryCode:   toCountryCode,
			PostCode:      lo.ToPtr(toPostCode),
		},
		ParcelDetails: &models.ParcelDetails{
			ShipperSubmittedWeight:     lo.ToPtr(gw),
			ShipperSubmittedWeightUnit: lo.ToPtr(wu),

			ShipperSubmittedDimensions: &models.Dimensions{
				Length: utils.ParseFloat64(null.StringFrom(request.Length)).Ptr(),
				Width:  utils.ParseFloat64(null.StringFrom(request.Width)).Ptr(),
				Height: utils.ParseFloat64(null.StringFrom(request.Height)).Ptr(),
				Unit:   lo.ToPtr(dimensionUnitInCm),
			},

			Weight:   lo.ToPtr(lw),
			Quantity: utils.ParseUint(null.StringFrom(request.ParcelQty)).Ptr(),
		},
		IsMMCCB2C:        true,
		RequestId:        request.BigBagID,
		PartnerUniqueKey: null.StringFrom(request.BigBagID).Ptr(),
	}
	return bagReq, nil
}

func (l *lazadaMMOrderCreator) validateParcelsInsideBag(ctx context.Context, partner *models.Partner, req *order.LazadaMMCreateBagRequest) ([]*models.Parcel, error) {
	refTIDs := lo.Map(req.ParcelList, func(p *order.LazadaMMParcelID, index int) string {
		return p.LogisticsOrderCode
	})

	mmccParcels, err := l.parcelRepo.GetMMCCParcelsByRefTIDsAndPartnerID(ctx, refTIDs, partner.ID)
	if err != nil {
		return nil, err
	}

	lzdParcelIDs := lo.SliceToMap(req.ParcelList, func(parcel *order.LazadaMMParcelID) (string, struct{}) {
		return l.lazadaParcelID(parcel.LogisticsOrderCode, parcel.TrackingNumber), struct{}{}
	})

	mmccParcels = lo.Filter(mmccParcels, func(p *models.Parcel, index int) bool {
		var md repo_interface.ParcelMetadata
		_ = json.Unmarshal([]byte(p.Metadata.String), &md)
		id := l.lazadaParcelID(p.RefTrackingID.String, md.ShipperRefNo)
		_, ok := lzdParcelIDs[id]
		return ok
	})

	if len(mmccParcels) == 0 {
		return nil, nil
	}

	linkedParcels := lo.Filter(mmccParcels, func(parcel *models.Parcel, index int) bool {
		return parcel.SourceOrderID.String != "" &&
			parcel.SourceOrderID.String != req.BigBagID
	})

	if len(linkedParcels) > 0 {
		return nil, fplerror.ErrParcelAssignedToBag
	}

	return mmccParcels, nil
}

func (l *lazadaMMOrderCreator) CreateMMCCParcelAIDC(ctx context.Context, partner *models.Partner, req *order.BaseRequest) error {
	uniqueId := &models.UniqueRequestID{
		RequestID: req.RequestId,
		IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
	}
	txn, err := repositories.BeginTransaction(ctx)
	if err != nil {
		return err
	}
	defer func() {
		err = repositories.ProcessTransaction(txn, err)
		if err != nil {
			loggerutils.Ctx(ctx, "lazada-mm-order-creator").Err(err).Msg("fail to process txn")
		}
	}()

	if err = l.orderRequestRepo.CreateUniqueRequestIdTxn(ctx, uniqueId, txn); err != nil {
		parcel, _ := l.parcelRepo.GetOneByIdentifier(ctx, repositories.NewMMCCParcelIdentifier(partner.ID, *req.PartnerUniqueKey))
		if parcel != nil {
			return nil
		}
		return fmt.Errorf("duplicated_order_request_id: %s, %w", req.RequestId, err)
	}

	if err = l.orderCreator.CreateMMCCParcel(ctx, &order_creation_interface.CreateMMCCParcelPayload{
		MMCCParcel: &order_creation_interface.MMCCParcel{
			Parcel:      req.ToParcel(partner.ID),
			ParcelItems: req.ToParcelItems(),
		},
		MoveBagOnDuplicated: false,
	}); err != nil {
		return fmt.Errorf("create lazada mm parcel error: %w", err)
	}
	return nil
}

func (l *lazadaMMOrderCreator) CreateAIDCMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaAIDCMMCreateBagRequest) (*models.Parcel, error) {
	ctx, span := nvtracer.CreateSpanFromContext(ctx)
	defer span.Finish()

	orderReqUniqueId := &models.UniqueRequestID{
		RequestID: req.IdempotentId,
		IDScheme:  configs.RequestIdSchemeLazadaMMParcelOC.String(),
	}

	txn, err := repositories.BeginTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		err = repositories.ProcessTransaction(txn, err)
		if err != nil {
			loggerutils.Ctx(ctx, "lazada-mm-order-creator").Err(err).Msg("fail to process txn")
		}
	}()
	identifyRequestErr := l.orderRequestRepo.CreateUniqueRequestIdTxn(ctx, orderReqUniqueId, txn)

	bag, err := l.parcelRepo.GetOne(ctx,
		models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(req.BigBagID)),
		models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
		models.ParcelWhere.Type.EQ(parcel.BagB2CV2),
	)
	if identifyRequestErr != nil {
		if err != nil {
			return nil, fmt.Errorf("failed_to_get_bag: %w: %v", fplerror.ErrInternal, err)
		}
		if bag != nil {
			return bag, nil
		}
		return nil, fmt.Errorf("duplicated_order_request_id: %w: %v", fplerror.ErrDuplicated, err)
	} else {
		if bag != nil {
			return nil, fmt.Errorf("duplicated_bag: %v: %s", fplerror.ErrDuplicated, req.BigBagID)
		}
	}

	mmccParcels, err := l.buzValidationForCreateBag(ctx, partner, req)
	if err != nil {
		return nil, err
	}

	bagReq, err := convertMMBagToOCReq(req)
	if err != nil {
		return nil, err
	}

	bag, err = l.orderCreator.CreateMMCCOrder(ctx, &order_creation_interface.MMCCOCPayload{
		BagRequest:  bagReq,
		MMCCParcels: nil,
	}, partner, txn)
	if err != nil {
		return nil, fmt.Errorf("can_not_create_bag: %w: %v", fplerror.ErrInternal, err)
	}

	err = l.parcelRepo.BulkUpdateTxn(ctx, *mmccParcels,
		models.M{
			models.ParcelColumns.ServiceID:      bag.ServiceID,
			models.ParcelColumns.ProductID:      bag.ProductID,
			models.ParcelColumns.ShipperID:      bag.ShipperID,
			models.ParcelColumns.ParentID:       bag.ID,
			models.ParcelColumns.SourceOrderID:  bag.SourceOrderID,
			models.ParcelColumns.OriginVendorID: bag.OriginVendorID,
		},
		txn,
	)

	if err != nil {
		return nil, fmt.Errorf("failed_to_link_bag_and_parcels %w", err)
	}
	return bag, nil
}

func (l *lazadaMMOrderCreator) aidcParcelID(orderCode string, trackingNumber string) string {
	return fmt.Sprintf("%s_%s", orderCode, trackingNumber)
}

func (l *lazadaMMOrderCreator) buzValidationForCreateBag(ctx context.Context, partner *models.Partner, req *order.LazadaAIDCMMCreateBagRequest) (*models.ParcelSlice, error) {
	refTIDs := lo.Map(req.ParcelList, func(p *order.LazadaMMParcel, index int) string {
		return p.OrderCode
	})

	mmccParcels, err := l.parcelRepo.GetMMCCParcelsByRefTIDsAndPartnerID(ctx, refTIDs, partner.ID)
	if err != nil {
		return nil, err
	}

	lzdParcelIDs := lo.SliceToMap(req.ParcelList, func(parcel *order.LazadaMMParcel) (string, struct{}) {
		return l.aidcParcelID(parcel.OrderCode, parcel.TrackingNumber), struct{}{}
	})

	mmccParcels = lo.Filter(mmccParcels, func(p *models.Parcel, index int) bool {
		_, ok := lzdParcelIDs[p.PartnerUniqueKey.String]
		return ok
	})

	if len(mmccParcels) == 0 {
		return nil, nil
	}

	if len(mmccParcels) < len(lo.Keys(lzdParcelIDs)) {
		return nil, fmt.Errorf("not all parcels created: %w: %v", fplerror.ErrParcelNotCreated, err)
	}

	// check parcels created in 4pl system
	mmccParcels, err = l.parcelRepo.GetListWithCtx(ctx,
		models.ParcelWhere.PartnerUniqueKey.IN(lo.Keys(lzdParcelIDs)),
		models.ParcelWhere.PartnerID.EQ(null.Uint64From(partner.ID)),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get parcels inside bag: %w: %v", fplerror.ErrInternal, err)
	}
	if len(mmccParcels) == 0 {
		return nil, fmt.Errorf("no parcels created yet: %w: %v", fplerror.ErrParcelNotCreated, err)
	}

	// check parcels assigned another bag
	assignedParcels := lo.Filter(mmccParcels, func(item *models.Parcel, index int) bool {
		return item.ParentID.Uint != 0 && item.SourceOrderID.String != ""
	})
	if len(assignedParcels) > 0 {
		return nil, fmt.Errorf("%w", fplerror.ErrParcelAssignedToBag)
	}
	return &mmccParcels, nil
}

func convertMMBagToOCReq(req *order.LazadaAIDCMMCreateBagRequest) (*order.BaseRequest, error) {
	serviceCodeMap, ok := cargoTypeAndToLocationToServiceCode[req.CargoType]
	if !ok {
		return nil, fplerror.ErrBadRequest.NewWithoutStack("unavailable service by cargo_type")
	}

	serviceCode, ok := serviceCodeMap[req.ToLocation]
	if !ok {
		return nil, fplerror.ErrBadRequest.NewWithoutStack("unavailable service by to_location")
	}
	if serviceCode == "" {
		return nil, fplerror.ErrBadRequest.NewWithoutStack("no service code found")
	}

	wu := req.WeightUnit
	gw := float64(req.BigBagWeight)
	lw := float64(req.BigBagWeight)
	if wu == "" || strings.EqualFold(wu, "g") {
		gw = gw / 1000
		lw = lw / 1000
		wu = "kg"
	}

	return &order.BaseRequest{
		ServiceCode:   serviceCode,
		Source:        order.SourceCustomAPI,
		SourceOrderID: lo.ToPtr(req.BigBagID),
		RefTrackingID: lo.ToPtr(req.TrackingNumber),
		From: order.Address{
			Name:         nvName,
			AddressLine1: fromAddressLine1,
			AddressLine2: lo.ToPtr(fromAddressLine2),
			AddressLine3: lo.ToPtr(fromAddressLine3),
			AddressLine4: lo.ToPtr(fromAddressLine4),

			City:          lo.ToPtr(fromCity),
			StateProvince: lo.ToPtr(fromStateProvince),
			CountryCode:   fromCountryCode,
			PostCode:      lo.ToPtr(fromPostCode),
		},
		To: order.Address{
			Name:          nvName,
			AddressLine1:  toAddressLine1,
			AddressLine2:  lo.ToPtr(toAddressLine2),
			City:          lo.ToPtr(toCity),
			StateProvince: lo.ToPtr(toStateProvince),
			CountryCode:   toCountryCode,
			PostCode:      lo.ToPtr(toPostCode),
		},
		ParcelDetails: &models.ParcelDetails{
			ShipperSubmittedWeight:     lo.ToPtr(gw),
			ShipperSubmittedWeightUnit: lo.ToPtr(wu),

			ShipperSubmittedDimensions: &models.Dimensions{
				Length: lo.ToPtr(float64(req.BigBagLength)),
				Width:  lo.ToPtr(float64(req.BigBagWidth)),
				Height: lo.ToPtr(float64(req.BigBagHeight)),
				Unit:   lo.ToPtr(req.DimensionUnit),
			},

			Weight:   lo.ToPtr(lw),
			Quantity: lo.ToPtr(uint(req.ParcelQty)),
		},
		IsMMCCB2C:        true,
		RequestId:        req.IdempotentId,
		PartnerUniqueKey: null.StringFrom(req.BigBagID).Ptr(),
	}, nil
}
