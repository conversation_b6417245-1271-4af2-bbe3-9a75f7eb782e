package orders

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/rs/zerolog/log"
	"github.com/volatiletech/null/v8"

	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvtracer"

	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	"git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/auth"
	"git.ninjavan.co/3pl/handlers/order_creation"
	"git.ninjavan.co/3pl/httpmodels/order"
	tplMiddleware "git.ninjavan.co/3pl/middleware"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

const lzdMMModuleName = "lazada-mm-order-controller"

type lazadaMMController struct {
	orderCreator order_creation.LazadaMMOrderCreator
}

func LazadaMMRouteGroup(rg *gin.RouterGroup) {
	ctrl := &lazadaMMController{
		orderCreator: order_creation.NewLazadaMMOrderCreator(),
	}

	rg.Group(
		"/lazada/mm",
	).POST(
		"/bag-create",
		auth.LazadaCainiaoMMAuthHandler(),
		tplMiddleware.SaveLazadaCainiaoMMOrderRequest(),
		ctrl.createMMCCBag,
	).POST(
		"/bag-update",
		auth.LazadaCainiaoMMAuthHandler(),
		tplMiddleware.SaveLazadaCainiaoMMOrderRequest(),
		ctrl.updateMMCCBag,
	).POST(
		"/parcel-create",
		auth.LazadaCainiaoMMAuthHandler(),
		tplMiddleware.SaveLazadaCainiaoMMOrderRequest(),
		ctrl.createMMCCParcel,
	).POST(
		"/parcel-create-aidc",
		auth.LazadaMMAuthHandler(),
		tplMiddleware.SaveLazadaMMOrderRequest(),
		ctrl.createMMCCParcelAIDC,
	).POST(
		"/bag-create-aidc",
		auth.LazadaMMAuthHandler(),
		tplMiddleware.SaveLazadaMMOrderRequest(),
		ctrl.createAIDCMMCCBag,
	)
}

// @Description	Create MMCC bag for Lazada MM
// @Accept			json
// @Produce		json
// @Tags			Lazada MM
// @Param			request	body		order.LazadaRequest	true	"Lazada MM bag creation request"
// @Success		200		{object}	order.LazadaBaseResponse
// @Failure		400		{object}	order.LazadaBaseResponse
// @Failure		500		{object}	order.LazadaBaseResponse
// @Router			/v1/lazada/mm/bag-create [post]
func (ctrl *lazadaMMController) createMMCCBag(c *gin.Context) {
	ctx, span := nvtracer.CreateSpanFromContext(c.Request.Context())
	defer span.Finish()
	logger := loggerutils.Ctx(c.Request.Context(), lzdMMModuleName)

	r := order.LazadaRequest{}
	if err := c.ShouldBindWith(&r, binding.Form); err != nil {
		logger.Err(err).Msg("[create bag] bind-request-error")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	ocRequest := &order.LazadaMMCreateBagRequest{}
	err := json.Unmarshal([]byte(r.LogisticsInterface), ocRequest)
	if err != nil {
		logger.Err(err).Msg("[create bag] unmarshal-request-error")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	if err = binding.Validator.ValidateStruct(ocRequest); err != nil {
		logger.Err(err).Msg("[create bag] validate-request-error")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	v, ok := c.Get(middlewareCfg.PartnerKey)
	if !ok {
		logger.Error().Msg("[create bag] Partner not found")
		c.JSON(http.StatusInternalServerError, order.NewLazadaErrorResponse(
			order.LazadaErrorCodeInternal,
			fmt.Sprintf("[create bag]an internal error occurred. big-bag-id: %s", ocRequest.BigBagID),
		))
		return
	}
	partner := v.(*models.Partner)
	bag, err := ctrl.orderCreator.CreateMMCCBag(ctx, partner, ocRequest)
	tplMiddleware.SetParcelContext(c, bag)

	if err != nil {
		switch {
		case errors.Is(err, fplerror.ErrBadRequest):
			log.Ctx(ctx).Info().Err(err).Msg("bad-request")
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		case errors.Is(err, fplerror.ErrParcelAssignedToBag):
			log.Ctx(ctx).Info().Err(err).Msg("error-parcels-assigned-to-another-bag")
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(
				order.LazadaErrorCodeInvalidRequest,
				fmt.Sprintf("[create bag] %s", err.Error())),
			)
		default:
			log.Ctx(ctx).Err(err).Msg("[create bag] error-creating-order")
			c.JSON(http.StatusInternalServerError,
				order.NewLazadaErrorResponse(
					order.LazadaErrorCodeInternal,
					fmt.Sprintf("[create bag] an internal error occurred. big-bag-id: %s", ocRequest.BigBagID),
				),
			)
		}
		return
	}

	c.JSON(http.StatusOK, order.LazadaBaseResponse{
		Success: true,
	})
}

// @Description	Update MMCC bag for Lazada MM
// @Accept			json
// @Produce		json
// @Tags			Lazada MM
// @Param			request	body		order.LazadaRequest	true	"Lazada MM bag update request"
// @Success		200		{object}	order.LazadaBaseResponse
// @Failure		400		{object}	order.LazadaBaseResponse
// @Failure		500		{object}	order.LazadaBaseResponse
// @Router			/v1/lazada/mm/bag-update [post]
func (ctrl *lazadaMMController) updateMMCCBag(c *gin.Context) {
	ctx, span := nvtracer.CreateSpanFromContext(c.Request.Context())
	defer span.Finish()
	logger := loggerutils.Ctx(c.Request.Context(), lzdMMModuleName)

	r := order.LazadaRequest{}
	if err := c.ShouldBindWith(&r, binding.Form); err != nil {
		logger.Err(err).Msg("[update-bag] bind-request-error")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	updateReq := &order.LazadaMMUpdateRequest{}
	err := json.Unmarshal([]byte(r.LogisticsInterface), updateReq)
	if err != nil {
		logger.Err(err).Msg("[update-bag] unmarshal-request-error")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	if err = binding.Validator.ValidateStruct(updateReq); err != nil {
		logger.Err(err).Msg("[update-bag] validate-request-error")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	v, ok := c.Get(middlewareCfg.PartnerKey)
	if !ok {
		logger.Error().Msg("[update-bag] Partner not found")
		c.JSON(http.StatusInternalServerError, order.NewLazadaErrorResponse(
			order.LazadaErrorCodeInternal,
			fmt.Sprintf("an internal error occurred. big-bag-id: %s", updateReq.BigBagID),
		))
		return
	}
	partner := v.(*models.Partner)
	err = ctrl.orderCreator.UpdateMMCCBag(ctx, partner, updateReq)
	if err != nil {
		logger.Err(err).Msg("[update-bag] error-updating-order")
		switch {
		case fplerror.ErrBadRequest.Equal(err):
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		case errors.Is(err, fplerror.ErrBagNotFound):
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, fmt.Sprintf("bag not found. big-bag-id: %s", updateReq.BigBagID)))
		default:
			c.JSON(http.StatusInternalServerError,
				order.NewLazadaErrorResponse(
					order.LazadaErrorCodeInternal,
					fmt.Sprintf("an internal error occurred. big-bag-id: %s", updateReq.BigBagID),
				),
			)
		}
		return
	}

	c.JSON(http.StatusOK, order.LazadaBaseResponse{
		Success: true,
	})
}

// @Description	Create MMCC parcel for Lazada MM
// @Accept			x-www-form-urlencoded
// @Produce		json
// @Tags			Lazada MM
// @Param			request	formData	order.LazadaRequest	true	"Lazada MM parcel creation request"
// @Success		200		{object}	order.LazadaBaseResponse
// @Failure		400		{object}	order.LazadaBaseResponse
// @Failure		500		{object}	order.LazadaBaseResponse
// @Router			/v1/lazada/mm/parcel-create [post]
func (ctrl *lazadaMMController) createMMCCParcel(c *gin.Context) {
	ctx, span := nvtracer.CreateSpanFromContext(c.Request.Context())
	defer span.Finish()

	logger := loggerutils.WithModule(log.Ctx(ctx), lzdMMModuleName)

	r := order.LazadaRequest{}
	if err := c.ShouldBindWith(&r, binding.Form); err != nil {
		logger.Info().Err(err).Msg(invalidRequestMsg)
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	ocRequest := &order.LazadaMMCreateParcelRequest{}
	err := json.Unmarshal([]byte(r.LogisticsInterface), ocRequest)
	if err != nil {
		logger.Info().Err(err).Msg(invalidRequestMsg)
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	if err = binding.Validator.ValidateStruct(ocRequest); err != nil {
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	parcelBaseReq := convertLazadaMmParcelToOCReq(ocRequest)

	v, _ := c.Get(middlewareCfg.PartnerKey)
	partner := v.(*models.Partner)
	err = ctrl.orderCreator.CreateMMCCParcel(ctx, partner, *parcelBaseReq.SourceOrderID, parcelBaseReq)
	if err != nil {
		logger.Err(err).Msg("[create-parcel] error-creating-lazada-parcel")
		switch {
		case errors.Is(err, fplerror.ErrBagNotFound):
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		case fplerror.ErrBadRequest.Equal(err):
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		default:
			c.JSON(http.StatusInternalServerError,
				order.NewLazadaErrorResponse(
					order.LazadaErrorCodeInternal,
					fmt.Sprintf("internal error when creating order. request-id: %s", ocRequest.TrackingNumber),
				))
		}
		return
	}

	c.JSON(http.StatusOK, order.LazadaBaseResponse{
		Success: true,
	})
}

// @Description	Create MMCC bag for Lazada MM V2
// @Accept			json
// @Produce		json
// @Tags			Lazada MM V2
// @Param			request	body		order.LazadaAIDCMMCreateBagRequest	true	"Lazada MM bag creation request"
// @Success		200		{object}	order.LazadaMMBagOCResponse
// @Failure		400		{object}	order.LazadaMMBagOCResponse
// @Failure		500		{object}	order.LazadaMMBagOCResponse
// @Router			/v1/lazada/mm/bag-create-aidc [post]
func (ctrl *lazadaMMController) createAIDCMMCCBag(c *gin.Context) {
	ctx, span := nvtracer.CreateSpanFromContext(c.Request.Context())
	defer span.Finish()
	logger := loggerutils.Ctx(c.Request.Context(), lzdMMModuleName)

	ocRequest := &order.LazadaAIDCMMCreateBagRequest{}
	if err := c.ShouldBindBodyWith(&ocRequest, binding.JSON); err != nil {
		logger.Err(err).Msg("failed-to-parse-request")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	if err := binding.Validator.ValidateStruct(ocRequest); err != nil {
		logger.Err(err).Msg("[create bag] validate-request-error")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	v, ok := c.Get(middlewareCfg.PartnerKey)
	if !ok {
		logger.Error().Msg("[create bag] Partner not found")
		c.JSON(http.StatusInternalServerError, order.NewLazadaErrorResponse(
			order.LazadaErrorCodeInternal,
			fmt.Sprintf("[create bag]an internal error occurred. big-bag-id: %s", ocRequest.BigBagID),
		))
		return
	}
	partner := v.(*models.Partner)
	_, err := ctrl.orderCreator.CreateAIDCMMCCBag(ctx, partner, ocRequest)
	if err != nil {
		switch {
		case fplerror.ErrorEqual(err, fplerror.ErrBadRequest):
			log.Ctx(ctx).Info().Err(err).Msg("bad-request")
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		case errors.Is(err, fplerror.ErrParcelAssignedToBag):
			log.Ctx(ctx).Info().Err(err).Msg("error-parcels-assigned-to-another-bag")
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(
				order.LazadaErrorCodeInvalidRequest,
				fmt.Sprintf("[create bag] %s", err.Error())),
			)
		case errors.Is(err, fplerror.ErrParcelNotCreated):
			log.Ctx(ctx).Info().Err(err).Msg("error-parcels-not-created-yet")
			c.JSON(http.StatusInternalServerError,
				order.NewLazadaErrorResponse(
					order.LazadaErrorCodeInternal,
					fmt.Sprintf("[create bag] parcels not created yet. big-bag-id: %s", ocRequest.BigBagID),
				),
			)
		default:
			log.Ctx(ctx).Err(err).Msg("[create bag] error-creating-order")
			c.JSON(http.StatusInternalServerError,
				order.NewLazadaErrorResponse(
					order.LazadaErrorCodeInternal,
					fmt.Sprintf("[create bag] an internal error occurred. big-bag-id: %s", ocRequest.BigBagID),
				),
			)
		}
		return
	}

	c.JSON(http.StatusOK, order.NewLazadaMMBagOCResponse(ocRequest.BigBagID))
}

func convertLazadaMmParcelToOCReq(request *order.LazadaMMCreateParcelRequest) *order.BaseRequest {
	fcc := utils.GetCountryInfoFromName(request.Sender.Address.Country).Alpha2
	if fcc == "" {
		fcc = request.Sender.Address.Country
	}
	tcc := utils.GetCountryInfoFromName(request.Receiver.Address.Country).Alpha2
	if tcc == "" {
		tcc = request.Receiver.Address.Country
	}

	from := order.Address{
		Name:          request.Sender.Name,
		AddressLine1:  request.Sender.Address.DetailAddress,
		AddressLine2:  null.StringFrom(request.Sender.Address.Street).Ptr(),
		AddressLine3:  null.StringFrom(request.Sender.Address.District).Ptr(),
		City:          null.StringFrom(request.Sender.Address.City).Ptr(),
		StateProvince: null.StringFrom(request.Sender.Address.Province).Ptr(),
		CountryCode:   fcc,
		ContactNumber: null.StringFrom(request.Sender.Phone).Ptr(),
		ContactEmail:  null.StringFrom(request.Sender.Email).Ptr(),
		PostCode:      null.StringFrom(request.Sender.ZipCode).Ptr(),
	}
	to := order.Address{
		Name:          request.Receiver.Name,
		AddressLine1:  request.Receiver.Address.DetailAddress,
		AddressLine2:  null.StringFrom(request.Receiver.Address.Street).Ptr(),
		AddressLine3:  null.StringFrom(request.Receiver.Address.District).Ptr(),
		City:          null.StringFrom(request.Receiver.Address.City).Ptr(),
		StateProvince: null.StringFrom(request.Receiver.Address.Province).Ptr(),
		CountryCode:   tcc,
		ContactNumber: null.StringFrom(request.Receiver.Phone).Ptr(),
		ContactEmail:  null.StringFrom(request.Receiver.Email).Ptr(),
		PostCode:      null.StringFrom(request.Receiver.ZipCode).Ptr(),
	}
	delivery := &order.Delivery{
		// Instructions: request.Remark,
	}

	w := utils.ParseFloat64(null.StringFrom(request.Parcel.Weight)).Float64
	wu := request.Parcel.WeightUnit
	if wu == "" || wu == "g" {
		w = w / 1000
		wu = "kg"
	}
	parcelReq := &order.BaseRequest{
		Source:        order.SourceCustomAPI,
		SourceOrderID: null.StringFrom(request.Parcel.BigBagID).Ptr(),
		RefTrackingID: null.StringFrom(request.LogisticsOrderCode).Ptr(),
		From:          from,
		To:            to,
		Delivery:      delivery,
		ParcelDetails: &models.ParcelDetails{
			ShipperSubmittedWeight:     null.Float64From(w).Ptr(),
			ShipperSubmittedWeightUnit: null.StringFrom(wu).Ptr(),
			Value:                      utils.ParseFloat64(null.StringFrom(request.Parcel.Price)).Ptr(),
			ShipperSubmittedDimensions: &models.Dimensions{
				Length: utils.ParseFloat64(null.StringFrom(request.Parcel.Length)).Ptr(),
				Width:  utils.ParseFloat64(null.StringFrom(request.Parcel.Width)).Ptr(),
				Height: utils.ParseFloat64(null.StringFrom(request.Parcel.Height)).Ptr(),
				Unit:   null.StringFrom(request.Parcel.DimensionUnit).Ptr(),
			},
			ShipperRefNo: null.StringFrom(request.TrackingNumber).String,
		},
		Items:            convertParcelItems(request),
		IsMMCCB2C:        true,
		RequestId:        request.TrackingNumber,
		PartnerUniqueKey: null.StringFrom(request.LogisticsOrderCode + "_" + request.TrackingNumber).Ptr(),
	}

	return parcelReq
}

func convertParcelItems(request *order.LazadaMMCreateParcelRequest) []*order.ParcelItem {
	var items []*order.ParcelItem
	for _, item := range request.Parcel.GoodsList {
		hsCode, _ := strconv.Atoi(item.HsCode)
		var taxMap order.TaxesMap
		if item.GstPaid == "Y" || item.LvgPaid == "true" {
			taxMap = make(order.TaxesMap)
			if item.GstPaid == "Y" {
				taxMap[parcel.GSTTaxName] = order.TaxInfo{
					Number:     null.StringFrom(item.GstRegistrationNumber).Ptr(),
					IsIncluded: null.BoolFrom(item.GstPaid == "Y").Ptr(),
				}
			}
			if item.LvgPaid == "true" {
				taxMap[parcel.LVGTaxName] = order.TaxInfo{
					Number:     null.StringFrom(item.Taxid).Ptr(),
					IsIncluded: utils.ParseBool(null.StringFrom(item.LvgPaid)).Ptr(),
				}
			}
		}

		w := utils.ParseFloat64(null.StringFrom(item.Weight)).Float64
		wu := item.WeightUnit
		if wu == "" || strings.EqualFold(wu, "g") {
			w = w / 1000
		}
		items = append(items, &order.ParcelItem{
			SKUId:                     null.StringFrom(item.SkuID).Ptr(),
			Description:               null.StringFrom(item.Name).Ptr(),
			NativeDescription:         null.StringFrom(item.CnName).Ptr(),
			UnitValue:                 utils.ParseFloat64(null.StringFrom(item.Price)).Ptr(),
			GoodsCurrency:             null.StringFrom(item.PriceCurrency).Ptr(),
			Quantity:                  utils.ParseInt(item.Quantity).Ptr(),
			Url:                       null.StringFrom(item.Url).Ptr(),
			HSCode:                    null.UintFrom(uint(hsCode)).Ptr(),
			UnitWeight:                null.Float64From(w).Ptr(),
			IsGstIncludedInGoodsValue: item.GstPaid == "Y",
			GstRegistrationNumber:     null.StringFrom(item.GstRegistrationNumber).Ptr(),
			Taxes:                     taxMap,
		})
	}
	return items
}

// @Description	Create MMCC parcel for Lazada MM
// @Accept			json
// @Produce		json
// @Tags			Lazada MM
// @Param			request	body		order.LazadaCreateParcelReq	true	"Lazada MM parcel creation request"
// @Success		200		{object}	order.LazadaBaseResponse
// @Failure		400		{object}	order.LazadaBaseResponse
// @Failure		500		{object}	order.LazadaBaseResponse
// @Router			/v1/lazada/mm/parcel-create-aidc [post]
func (ctrl *lazadaMMController) createMMCCParcelAIDC(c *gin.Context) {
	ctx, span := nvtracer.CreateSpanFromContext(c.Request.Context())
	defer span.Finish()

	logger := loggerutils.Ctx(c.Request.Context(), lzdMMModuleName)
	r := order.LazadaCreateParcelReq{}
	if err := c.ShouldBindBodyWith(&r, binding.JSON); err != nil {
		logger.Err(err).Msg("failed-to-parse-request")
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	v, _ := c.Get(middlewareCfg.PartnerKey)
	partner := v.(*models.Partner)
	req := convertLazadaParcelToOCReq(r)
	req.SourceOrderID = nil
	req.RefTrackingID = null.StringFrom(r.OrderCode).Ptr()
	req.ParcelDetails.ShipperRefNo = r.TrackingNumber

	err := ctrl.orderCreator.CreateMMCCParcelAIDC(ctx, partner, req)
	if err != nil {
		logger.Err(err).Msg("error-creating-lazada-mm-parcel-order")
		switch {
		case fplerror.ErrBadRequest.Equal(err):
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, order.NewLazadaErrorResponse(order.LazadaErrorCodeInternal, "internal error when creating order"))
		}
		return
	}
	c.JSON(http.StatusOK, order.NewLazadaMMOCResponse(r.OrderCode))
}
